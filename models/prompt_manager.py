import tiktoken  # or any tokenizer compatible with your model


class PromptManager:
    def __init__(self, max_context_tokens: int = 4096, reserved_tokens: int = 50):
        """
        Args:
            max_context_tokens: Maximum total tokens allowed (prompt + generation).
            reserved_tokens: Tokens reserved for special tokens or safety margin.
        """
        self.max_context_tokens = max_context_tokens  # TODO: use get_context_length_from_server from utils.model_info.py to fetch this - also remove from config.py
        self.reserved_tokens = reserved_tokens
        self.tokenizer = tiktoken.get_encoding("cl100k_base")  # Replace with your tokenizer

    def count_tokens(self, text: str) -> int:
        """Count tokens in a given text string."""
        tokens = self.tokenizer.encode(text)
        return len(tokens)

    def prompt_token_length(self, prompt: str) -> int:
        """Return token length of the prompt."""
        return self.count_tokens(prompt)

    def adjust_max_tokens(self, prompt: str, requested_max_tokens: int) -> int:
        """
        Adjust max_tokens to ensure (prompt tokens + max_tokens) <= max_context_tokens - reserved_tokens.

        Args:
            prompt: The prompt string to be sent to the model.
            requested_max_tokens: The max tokens client wants to generate.

        Returns:
            Adjusted max_tokens that fits within context window.
        """
        prompt_len = self.prompt_token_length(prompt)
        available_tokens = self.max_context_tokens - prompt_len - self.reserved_tokens
        adjusted_max_tokens = min(requested_max_tokens, max(0, available_tokens))
        return adjusted_max_tokens
