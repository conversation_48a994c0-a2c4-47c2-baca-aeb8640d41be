"""
Configuration constants for the DaBot Agent.

This module contains all configuration constants and settings for the application.
"""

from typing import Dict, Any

APP_VERSION = "0.1"
APP_NAME = "DaBot Agent"
LLAMACPP_API_URL = "http://172.16.0.111:11111/completion"
LLAMACPP_MAX_TOKENS = 250  # n_predict
MODEL_PROVIDER = "llamacpp"
MODEL_NAME = "ollama7b-finance"  # TODO: verify where is it used and point them to get_model_name_from_server from utils.model_info instead of the constant, then remove from config
MAX_ITERATIONS = "1"
VERBOSE = "true"
LOG_LEVEL = "DEBUG"
LOG_FILE = "dabot-agent.log"
_DEFAULT_MAX_CONTEXT_TOKENS = 2048  # TODO: remove from config we wont use this
RESERVED_TOKENS = 50
MAX_CONTEXT_TOKENS = get_max_context_tokens()  # TODO: verify where is it used and point them to get_max_context_tokens from utils.model_info instead of the constant, then remove from config


# =============================================================================
# Helper Functions
# =============================================================================
def get_model_config() -> Dict[str, Any]:
    """Get model configuration dictionary."""
    return {
            "provider"  : MODEL_PROVIDER,
            "name"      : MODEL_NAME,
            "max_tokens": LLAMACPP_MAX_TOKENS,
            "api_url"   : LLAMACPP_API_URL
    }


def get_agent_config() -> Dict[str, Any]:
    """Get agent configuration dictionary."""
    return {
            "max_iterations": MAX_ITERATIONS,
            "verbose"       : VERBOSE
    }


def get_logging_config() -> Dict[str, Any]:
    """Get logging configuration dictionary."""
    return {
            "level": LOG_LEVEL,
            "file" : LOG_FILE
    }


def get_max_context_tokens() -> int:
    """
    Fetch MAX_CONTEXT_TOKENS from the remote server at runtime.
    Falls back to default value if server is unavailable.

    Returns:
        Context tokens from server or default fallback value
    """
    try:
        import requests
        import json

        # Convert completion endpoint to props endpoint
        props_url = LLAMACPP_API_URL.replace('/completion', '/props')

        response = requests.get(props_url, timeout = 3)
        response.raise_for_status()

        props = response.json()
        n_ctx = props.get('default_generation_settings', {}).get('n_ctx')

        if n_ctx and isinstance(n_ctx, int) and n_ctx > 0:
            return n_ctx
        else:
            return _DEFAULT_MAX_CONTEXT_TOKENS

    except Exception:
        # Silently fall back to default if server unavailable or any error
        return _DEFAULT_MAX_CONTEXT_TOKENS
